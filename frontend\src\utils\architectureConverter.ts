import { ArchitectureNode, ArchitectureEdge, CloudService } from '@/types/architecture'
import { getServiceById, AWS_SERVICES } from '@/components/architecture/utils/serviceDefinitions'

// MapleGUI service name and service code to our service ID mapping
const MAPLEGUI_SERVICE_MAPPING: Record<string, string> = {
  // Service Names (from PyQt5 MapleGUI pickle files)
  'Amazon EC2': 'aws-ec2',
  'Amazon S3': 'aws-s3',
  'Amazon Simple Storage Service': 'aws-s3', // Full name from backend
  'Amazon Simple Storage System': 'aws-s3', // Alternative name from image filename
  'Amazon Simple Storage System (S3)': 'aws-s3', // Full alternative name
  'Amazon DynamoDB': 'aws-dynamodb',
  'Amazon Lambda': 'aws-lambda',
  'Amazon RDS': 'aws-rds',
  'Amazon CloudFront': 'aws-cloudfront',
  'Amazon API Gateway': 'aws-api-gateway',
  'Amazon SQS': 'aws-sqs',
  'Amazon SNS': 'aws-sns',
  'Amazon ECS': 'aws-ecs',
  'Amazon EKS': 'amazon-eks',
  'Amazon ElastiCache': 'aws-elasticache',
  'Amazon Redshift': 'amazon-redshift',
  'Amazon Kinesis': 'aws-kinesis',
  'Amazon SageMaker': 'aws-sagemaker',
  'Amazon Athena': 'amazon-athena',
  'Amazon EMR': 'aws-emr',
  'Amazon Glue': 'aws-glue',
  'Amazon QuickSight': 'amazon-quicksight',
  'Amazon Comprehend': 'amazon-comprehend',

  // Specific services from the PyQt5 architecture
  'AWS Greengrass': 'aws-greengrass',
  'AWS IoT Core': 'aws-iot-core',
  'Amazon Kinesis Data Firehose': 'amazon-kinesis-data-firehose',
  'S3': 'aws-s3',
  'User': 'user',

  // Service Codes (from attributes.servicecode) - these are what PyQt5 MapleGUI actually stores
  'AWSLambda': 'aws-lambda',
  'AmazonS3': 'aws-s3',
  'AmazonEC2': 'aws-ec2',
  'AmazonDynamoDB': 'aws-dynamodb',
  'AmazonRDS': 'aws-rds',
  'AmazonCloudFront': 'aws-cloudfront',
  'AmazonApiGateway': 'aws-api-gateway',
  'AmazonSQS': 'aws-sqs',
  'AmazonSNS': 'aws-sns',
  'AmazonECS': 'aws-ecs',
  'AmazonEKS': 'amazon-eks',
  'AmazonElastiCache': 'aws-elasticache',
  'AmazonRedshift': 'amazon-redshift',
  'AmazonKinesis': 'aws-kinesis',
  'AmazonSageMaker': 'aws-sagemaker',
  'AmazonAthena': 'amazon-athena',
  'AmazonEMR': 'aws-emr',
  'AmazonGlue': 'aws-glue',
  'AmazonQuickSight': 'amazon-quicksight',
  'AmazonComprehend': 'amazon-comprehend',
  'AmazonRekognition': 'aws-rekognition',
  'AmazonTextract': 'aws-textract',
  'AmazonTranslate': 'aws-translate',
  'AmazonPolly': 'aws-polly',
  'AmazonTranscribe': 'aws-transcribe',
  'AmazonLex': 'aws-lex',
  'AmazonBedrock': 'aws-bedrock',
  'AWSBatch': 'aws-batch',
  'AWSFargate': 'aws-fargate',
  'AWSStepFunctions': 'aws-step-functions',
  'AmazonEventBridge': 'amazon-eventbridge',
  'AWSCloudFormation': 'aws-cloudformation',
  'AWSCloudTrail': 'aws-cloudtrail',
  'AmazonCloudWatch': 'aws-cloudwatch',
  'AWSIAM': 'aws-iam',
  'AWSKMS': 'aws-kms',
  'AWSSecretsManager': 'aws-secrets-manager',
  'AmazonVPC': 'aws-vpc',
  'AmazonRoute53': 'aws-route53',
  'AWSDirectConnect': 'aws-direct-connect',
  'ElasticLoadBalancing': 'elastic-load-balancing',
  'AWSAutoScaling': 'aws-auto-scaling',
  'AmazonSimpleStorageService': 'aws-s3',  // Alternative name for S3

  // Additional unique service codes (no duplicates)
  'AmazonCloudSearch': 'amazon-cloudsearch',
  'AmazonES': 'amazon-elasticsearch-service',
  'AmazonFinSpace': 'amazon-finspace',
  'AmazonKinesisAnalytics': 'amazon-kinesis-data-analytics',
  'AmazonMSK': 'amazon-managed-streaming-for-apache-kafka',
  'AWSDataExchange': 'aws-data-exchange',
  'AWSGlueDataBrew': 'aws-glue-databrew',
  'AmazonManagedGrafana': 'amazon-managed-grafana',
  'AmazonManagedServiceforPrometheus': 'amazon-managed-service-for-prometheus',
  'AmazonOpenSearchService': 'amazon-opensearch-service',
  'AmazonKinesisDataFirehose': 'amazon-kinesis-data-firehose',
  'AWSDataPipeline': 'aws-data-pipeline',
  'AWSLakeFormation': 'aws-lake-formation',
  'AmazonAppFlow': 'amazon-appflow',
  'AmazonMWAA': 'amazon-mwaa',
  'AmazonMQ': 'amazon-mq',
  'AWSAppSync': 'aws-appsync',
  'AWSElasticBeanstalk': 'aws-elastic-beanstalk',
  'AWSLightsail': 'aws-lightsail',
  'AWSOutposts': 'aws-outposts',
  'AWSWavelength': 'aws-wavelength',
  'AWSLocalZones': 'aws-local-zones',
  'AmazonECR': 'amazon-ecr',
  'AWSServerlessApplicationRepository': 'aws-serverless-application-repository',
  'AmazonEBS': 'amazon-ebs',
  'AmazonEFS': 'amazon-efs',
  'AmazonFSx': 'amazon-fsx-lustre',
  'AWSStorageGateway': 'aws-storage-gateway',
  'AWSBackup': 'aws-backup',
  'AWSSnowFamily': 'aws-snow-family',
  'AmazonAurora': 'amazon-aurora',
  'AmazonDocumentDB': 'amazon-documentdb',
  'AmazonMemoryDB': 'amazon-memorydb',
  'AmazonNeptune': 'amazon-neptune',
  'AmazonQLDB': 'amazon-qldb',
  'AmazonTimestream': 'amazon-timestream',
  'AmazonKeyspaces': 'amazon-keyspaces',
  'AWSIoTCore': 'aws-iot-core',
  'AWSIoTGreengrass': 'aws-greengrass',
  'AWSIoTAnalytics': 'aws-iot-analytics',
  'AWSIoTDevice': 'aws-iot-device-management',
  'AWSIoTEvents': 'aws-iot-events',
  'AWSIoTSiteWise': 'aws-iot-sitewise',
  'AWSIoTThingsGraph': 'aws-iot-things-graph',
  'AWSIoT1Click': 'aws-iot-1-click',
  'AWSIoTButton': 'aws-iot-button',
  'AWSIoTDeviceDefender': 'aws-iot-device-defender',
  'AWSIoTFleetHub': 'aws-iot-fleet-hub',
  'AmazonCodeWhisperer': 'amazon-codewhisperer',
  'AmazonKendra': 'amazon-kendra',
  'AmazonPersonalize': 'amazon-personalize',
  'AmazonForecast': 'amazon-forecast',
  'AmazonFraudDetector': 'amazon-fraud-detector',
  'AmazonDevOpsGuru': 'amazon-devops-guru',
  'AmazonCodeGuru': 'amazon-codeguru',
  'AmazonAugmentedAI': 'amazon-augmented-ai',
  'AmazonLookoutforVision': 'amazon-lookout-for-vision',
  'AmazonLookoutforEquipment': 'amazon-lookout-for-equipment',
  'AmazonLookoutforMetrics': 'amazon-lookout-for-metrics',
  'AmazonMonitron': 'amazon-monitron',
  'AmazonHealthLake': 'amazon-healthlake',
  'AWSPanorama': 'aws-panorama',
  'AWSDeepRacer': 'aws-deepracer',
  'AWSDeepLens': 'aws-deeplens',
  'AWSDeepComposer': 'aws-deepcomposer',
  'AmazonBraket': 'amazon-braket',
  'AWSRoboMaker': 'aws-robomaker',
  'AWSGroundStation': 'aws-ground-station',
  'AWSSatellite': 'aws-satellite',
  'AmazonGameLift': 'amazon-gamelift',
  'AmazonLumberyard': 'amazon-lumberyard',
  'AWSGameSparks': 'aws-gamesparks',
  'AmazonIVS': 'amazon-ivs',
  'AmazonNimbleStudio': 'amazon-nimble-studio',
  'AWSElementalMediaConnect': 'aws-elemental-mediaconnect',
  'AWSElementalMediaConvert': 'aws-elemental-mediaconvert',
  'AWSElementalMediaLive': 'aws-elemental-medialive',
  'AWSElementalMediaPackage': 'aws-elemental-mediapackage',
  'AWSElementalMediaStore': 'aws-elemental-mediastore',
  'AWSElementalMediaTailor': 'aws-elemental-mediatailor',
  'AmazonElasticTranscoder': 'amazon-elastic-transcoder',
  'AWSThinkbox': 'aws-thinkbox',
  'AmazonConnect': 'aws-connect',
  'AmazonPinpoint': 'amazon-pinpoint',
  'AmazonSES': 'amazon-ses',
  'AmazonWorkDocs': 'amazon-workdocs',
  'AmazonWorkMail': 'amazon-workmail',
  'AmazonWorkLink': 'amazon-worklink',
  'AWSClientVPN': 'aws-client-vpn',
  'AWSGlobalAccelerator': 'aws-global-accelerator',
  'AWSPrivateLink': 'aws-privatelink',
  'AWSTransitGateway': 'aws-transit-gateway',
  'AWSCloudMap': 'aws-cloud-map',
  'AWSAppMesh': 'aws-app-mesh',
  'AWSCloudWAN': 'aws-cloud-wan',
  'AWSDirectoryService': 'aws-directory-service',
  'AWSRAM': 'aws-ram',
  'AWSOrganizations': 'aws-organizations',
  'AWSControlTower': 'aws-control-tower',
  'AWSLicenseManager': 'aws-license-manager',
  'AWSCertificateManager': 'aws-certificate-manager',
  'AWSCloudHSM': 'aws-cloudhsm',
  'AWSGuardDuty': 'amazon-guardduty',
  'AmazonInspector': 'amazon-inspector',
  'AmazonMacie': 'amazon-macie',
  'AWSSecurityHub': 'aws-security-hub',
  'AWSShield': 'aws-shield',
  'AWSWAF': 'aws-waf',
  'AWSFirewallManager': 'aws-firewall-manager',
  'AWSArtifact': 'aws-artifact',
  'AWSAuditManager': 'aws-audit-manager',
  'AWSConfig': 'aws-config',
  'AWSManagedServices': 'aws-managed-services',
  'AWSOpsCenterforSystems': 'aws-opscenter',
  'AWSPersonalHealthDashboard': 'aws-personal-health-dashboard',
  'AWSProton': 'aws-proton',
  'AWSServiceCatalog': 'aws-service-catalog',
  'AWSSystemsManager': 'aws-systems-manager',
  'AWSTrustedAdvisor': 'aws-trusted-advisor',
  'AWSWellArchitectedTool': 'aws-well-architected-tool',
  'AWSChatbot': 'aws-chatbot',
  'AWSConsoleHome': 'aws-console-home',
  'AWSConsoleMobileApplication': 'aws-console-mobile-application',
  'AWSResourceGroups': 'aws-resource-groups',
  'AWSTagEditor': 'aws-tag-editor',
  'AWSAmplify': 'aws-amplify',
  'AWSAppRunner': 'aws-apprunner',
  'AWSDeviceFarm': 'aws-device-farm',
  'AWSHoneycode': 'aws-honeycode',
  'AmazonLocationService': 'amazon-location-service'
}

interface MapleGUIItem {
  object: {
    name: string
    Cost: string | number
    Latency: string | number
    attributes: Record<string, any>
    text: string
    RowNum?: number
  }
  pos: [number, number]
  cost: string | number
  latency: string | number
  attributes: Record<string, any>
}

interface MapleGUIData {
  items: MapleGUIItem[]
  adjacency_matrix: Array<[any, any, any?]>
  arch_type?: string
  mappings?: Record<string, any>
  vpcsquares?: Record<string, any>
  vpclist?: any[]
  name?: string
}

export async function convertMapleGUIToReactFlow(data: MapleGUIData): Promise<{
  nodes: ArchitectureNode[]
  edges: ArchitectureEdge[]
}> {
  const nodes: ArchitectureNode[] = []
  const edges: ArchitectureEdge[] = []
  const nodeMap = new Map<any, string>() // Map original object IDs to node IDs

  // Convert items to nodes
  for (let i = 0; i < data.items.length; i++) {
    const item = data.items[i]
    // Create node ID - use original React ID if available, otherwise use index
    const nodeId = item.object?.original_react_id || `node-${i}`
    console.log(`Creating node with ID: ${nodeId} (original: ${item.object?.original_react_id}, index: ${i})`)

    // Map original object ID to node ID for edge creation
    // Use object ID if available, otherwise use the object reference
    const objectId = (item.object as any).id || item.object
    nodeMap.set(objectId, nodeId)

    // Find matching service - use the original service name from PyQt5
    const serviceName = item.object.name
    let service: CloudService | undefined

    console.log(`Processing service: "${serviceName}" with attributes:`, item.object.attributes)

    // PyQt5 MapleGUI service name mapping - try exact matches first
    const cleanServiceName = serviceName.trim()
    console.log(`Clean service name: "${cleanServiceName}"`)

    // Try direct mapping first
    const mappedServiceId = MAPLEGUI_SERVICE_MAPPING[cleanServiceName]
    if (mappedServiceId) {
      console.log(`Found direct mapping: "${cleanServiceName}" -> "${mappedServiceId}"`)
      service = getServiceById(mappedServiceId)
      if (service) {
        console.log(`Successfully found service definition for: "${service.name}"`)
      } else {
        console.warn(`Service definition not found for mapped ID: "${mappedServiceId}"`)
      }
    }

    // If not found, try fuzzy matching with the original service name
    if (!service) {
      console.log(`No direct mapping found, trying fuzzy matching for: "${cleanServiceName}"`)
      service = findBestMatchingService(cleanServiceName)
      if (service) {
        console.log(`Found fuzzy match: "${cleanServiceName}" -> "${service.id}" (${service.name})`)
      }
    }

    // Create default service if no match found - preserve the original name
    if (!service) {
      console.log(`No match found, creating default service for: "${cleanServiceName}"`)
      console.log(`Available mappings:`, Object.keys(MAPLEGUI_SERVICE_MAPPING))
      service = createDefaultService(cleanServiceName, item.object.text)
      console.log(`Created default service: "${service.name}" with ID: "${service.id}"`)
    }

    // Convert position (MapleGUI uses different coordinate system)
    const position = {
      x: item.pos[0],
      y: item.pos[1]
    }

    // Convert attributes and configuration
    const config = convertAttributes(item.attributes, service)

    // Create node with PyQt5-compatible labeling
    const displayLabel = cleanServiceName === 'User' ? 'User' : cleanServiceName.replace('Amazon ', '')

    const node: ArchitectureNode = {
      id: nodeId,
      type: cleanServiceName === 'User' ? 'userNode' : 'awsService',
      position,
      data: {
        service,
        config,
        label: displayLabel,
        originalName: cleanServiceName, // Preserve the original service name
        cost: parseFloat(String(item.cost)) || 0,
        latency: parseFloat(String(item.latency)) || 0
      }
    }

    nodes.push(node)
  }

  // Convert adjacency matrix to edges
  if (data.adjacency_matrix) {
    console.log(`Processing ${data.adjacency_matrix.length} connections from adjacency matrix`)
    console.log(`Available nodes for mapping:`, nodes.map(n => ({ id: n.id, name: n.data.label })))

    for (let i = 0; i < data.adjacency_matrix.length; i++) {
      const [sourceObj, targetObj, connectionData] = data.adjacency_matrix[i]

      console.log(`Processing connection ${i}:`, { sourceObj, targetObj, connectionData })

      // The backend sends objects with 'id' and 'name' properties
      // We need to map these backend IDs to our frontend node IDs
      let sourceId, targetId

      if (sourceObj && typeof sourceObj === 'object' && 'id' in sourceObj) {
        // Backend sends the object index as 'id', we need to convert to our node ID format
        const backendSourceId = sourceObj.id

        // Skip connections with unmapped objects (backend returns -1 for unmapped)
        if (backendSourceId === -1) {
          console.warn(`Skipping connection ${i}: source object unmapped (ID: -1, name: ${sourceObj.name})`)
          continue
        }

        // Check if we have the original React ID for better mapping
        if (sourceObj.original_react_id) {
          sourceId = sourceObj.original_react_id
          console.log(`Using original React ID for source: ${sourceId}`)
        } else if (backendSourceId !== null && backendSourceId !== undefined && backendSourceId >= 0) {
          sourceId = `node-${backendSourceId}`
          console.log(`Mapped source backend ID ${backendSourceId} to frontend ID ${sourceId}`)
        } else {
          // Fallback: try to find by name matching
          const sourceName = sourceObj.name
          const matchingNode = nodes.find(node =>
            node.data.originalName === sourceName ||
            node.data.label === sourceName ||
            node.data.service?.name === sourceName
          )
          if (matchingNode) {
            sourceId = matchingNode.id
            console.log(`Found source by name matching: ${sourceName} -> ${sourceId}`)
          } else {
            console.warn(`Could not map source object with ID ${backendSourceId} and name ${sourceName}`)
            // Skip this connection if we can't map the source
            continue
          }
        }
      } else {
        console.warn(`Invalid source object in connection ${i}:`, sourceObj)
        continue
      }

      if (targetObj && typeof targetObj === 'object' && 'id' in targetObj) {
        // Backend sends the object index as 'id', we need to convert to our node ID format
        const backendTargetId = targetObj.id

        // Skip connections with unmapped objects (backend returns -1 for unmapped)
        if (backendTargetId === -1) {
          console.warn(`Skipping connection ${i}: target object unmapped (ID: -1, name: ${targetObj.name})`)
          continue
        }

        // Check if we have the original React ID for better mapping
        if (targetObj.original_react_id) {
          targetId = targetObj.original_react_id
          console.log(`Using original React ID for target: ${targetId}`)
        } else if (backendTargetId !== null && backendTargetId !== undefined && backendTargetId >= 0) {
          targetId = `node-${backendTargetId}`
          console.log(`Mapped target backend ID ${backendTargetId} to frontend ID ${targetId}`)
        } else {
          // Fallback: try to find by name matching
          const targetName = targetObj.name
          const matchingNode = nodes.find(node =>
            node.data.originalName === targetName ||
            node.data.label === targetName ||
            node.data.service?.name === targetName
          )
          if (matchingNode) {
            targetId = matchingNode.id
            console.log(`Found target by name matching: ${targetName} -> ${targetId}`)
          } else {
            console.warn(`Could not map target object with ID ${backendTargetId} and name ${targetName}`)
            // Skip this connection if we can't map the target
            continue
          }
        }
      } else {
        console.warn(`Invalid target object in connection ${i}:`, targetObj)
        continue
      }

      console.log(`Connection ${i}: sourceId=${sourceId}, targetId=${targetId}`)

      // Verify that both source and target nodes exist
      const sourceExists = nodes.some(node => node.id === sourceId)
      const targetExists = nodes.some(node => node.id === targetId)

      if (sourceId && targetId && sourceExists && targetExists) {
        // Create unique edge ID to avoid conflicts
        const edgeId = `edge-${sourceId}-${targetId}-${i}`

        const edge: ArchitectureEdge = {
          id: edgeId,
          source: sourceId,
          target: targetId,
          type: 'smoothstep',
          animated: false,
          markerEnd: 'arrowclosed',
          data: {
            cost: connectionData?.cost || 0,
            latency: connectionData?.latency || 0
          },
          style: {
            stroke: '#6366f1',
            strokeWidth: 2
          }
        }

        edges.push(edge)
        console.log(`✅ Created edge ${edgeId}: ${sourceId} -> ${targetId} (${sourceObj.name} -> ${targetObj.name})`)
      } else {
        console.warn(`❌ Skipping connection ${i}: sourceId=${sourceId} (exists: ${sourceExists}), targetId=${targetId} (exists: ${targetExists})`)
        console.warn(`   sourceObj:`, sourceObj, 'targetObj:', targetObj)
      }
    }
  }

  console.log(`✅ Created ${edges.length} edges from ${data.adjacency_matrix?.length || 0} connections`)

  // Debug: Print final node and edge summary
  console.log('🔍 Final conversion summary:')
  console.log(`  Nodes: ${nodes.length}`)
  nodes.forEach((node, i) => {
    console.log(`    Node ${i}: ${node.id} (${node.data.label})`)
  })
  console.log(`  Edges: ${edges.length}`)
  edges.forEach((edge, i) => {
    console.log(`    Edge ${i}: ${edge.source} -> ${edge.target}`)
  })

  return { nodes, edges }
}

function findBestMatchingService(serviceName: string): CloudService | undefined {
  const normalizedName = serviceName.toLowerCase().replace(/[^a-z0-9]/g, '')

  for (const service of AWS_SERVICES) {
    const normalizedServiceName = service.name.toLowerCase().replace(/[^a-z0-9]/g, '')
    if (normalizedServiceName.includes(normalizedName) || normalizedName.includes(normalizedServiceName)) {
      return service
    }
  }

  return undefined
}

function createDefaultService(serviceName: string, providerText: string): CloudService {
  const provider = providerText === 'GCP' ? 'GCP' : providerText === 'AZR' ? 'Azure' : 'AWS'

  // Special case for User service - use consistent ID
  if (serviceName.toLowerCase() === 'user') {
    return {
      id: 'user',
      name: 'User',
      provider,
      category: 'User',
      icon: '👤',
      fallbackIcon: '👤',
      description: 'End users or clients accessing the system',
      defaultConfig: {
        userCount: 1000,
        location: 'Global'
      },
      costModel: 'user_cost_model',
      latencyModel: 'user_latency_model',
      color: '#8B5CF6'
    }
  }

  return {
    id: `unknown-${serviceName.toLowerCase().replace(/[^a-z0-9]/g, '-')}`,
    name: serviceName,
    provider,
    category: 'Compute',
    icon: '/icons/aws/default.png',
    fallbackIcon: '❓',
    description: `Unknown service: ${serviceName}`,
    defaultConfig: {},
    costModel: 'unknown_cost_model',
    latencyModel: 'unknown_latency_model',
    color: '#999999'
  }
}

function convertAttributes(attributes: Record<string, any>, service: CloudService): Record<string, any> {
  if (!attributes || typeof attributes !== 'object') {
    return service.defaultConfig || {}
  }

  // Start with service default config
  const config = { ...service.defaultConfig }

  // Map common attribute names
  const attributeMapping: Record<string, string> = {
    'instanceType': 'instanceType',
    'instance_type': 'instanceType',
    'Instance Type': 'instanceType',
    'memory': 'memory',
    'Memory': 'memory',
    'storage': 'storage',
    'Storage': 'storage',
    'workload': 'workload',
    'Workload': 'workload',
    'region': 'region',
    'Region': 'region'
  }

  // Convert attributes
  for (const [key, value] of Object.entries(attributes)) {
    const mappedKey = attributeMapping[key] || key
    config[mappedKey] = value
  }

  return config
}
