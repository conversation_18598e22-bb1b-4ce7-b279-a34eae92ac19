"""
Architecture API endpoints for loading and saving architecture files.
Provides compatibility with PyQt5 MapleGUI pickle files and database storage.
"""

import pickle
import json
import tempfile
import os
import io
from datetime import datetime
from typing import Dict, Any, List, Tuple, Optional
from fastapi import APIRouter, UploadFile, File, HTTPException, Depends
from fastapi.responses import JSONResponse, Response
from sqlalchemy.orm import Session
from app.database.base import get_db
from app.database.models import User, Architecture
from app.auth.dependencies import get_current_user

router = APIRouter()

# Mock classes to handle PyQt5 MapleGUI pickle deserialization
class MockShape:
    """Mock Shape class to handle pickle deserialization"""
    def __init__(self, *args, **kwargs):
        # Initialize with default values - but don't set name here
        # The name will be set during pickle restoration
        self.Cost = 0
        self.Latency = 0
        self.attributes = {}
        self.text = 'S'
        self.RowNum = None
        self.a = 0
        self.b = 0
        self.Type1 = None
        self.CostDesc = ""
        self.LatencyDesc = ""
        self.group = None
        self.CloudLatency = 0.0
        self.paramList = None
        self.compareList = None
        self.workLoad = None
        self.region = "ap-south-1"
        self.in_arrows = []
        self.out_arrows = []
        self.selectedTime = None
        self.tfrs = []

        # Handle constructor arguments if any
        if args:
            if len(args) >= 1:
                self.text = args[0]
            if len(args) >= 3:
                self.a = args[1]
                self.b = args[2]
            if len(args) >= 5:
                self.Cost = args[3]
                self.Latency = args[4]
            if len(args) >= 6:
                self.RowNum = args[5]

        # Handle keyword arguments
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __setstate__(self, state):
        """Handle pickle state restoration"""
        print(f"MockShape.__setstate__ called with state keys: {list(state.keys()) if isinstance(state, dict) else 'Not a dict'}")

        # Update with all state attributes - this preserves the original service names
        self.__dict__.update(state)

        # Debug: Print the name after state restoration
        if hasattr(self, 'name'):
            print(f"MockShape name after __setstate__: {self.name}")
        else:
            print("MockShape has no name attribute after __setstate__")

        # Only set defaults for missing attributes, don't override existing ones
        if not hasattr(self, 'Cost'):
            self.Cost = 0
        if not hasattr(self, 'Latency'):
            self.Latency = 0
        if not hasattr(self, 'attributes'):
            self.attributes = {}
        if not hasattr(self, 'text'):
            self.text = 'S'

        # If no name is set, try to extract it from attributes
        if not hasattr(self, 'name') or not self.name:
            if hasattr(self, 'attributes') and isinstance(self.attributes, dict) and self.attributes:
                # Try to get service name from attributes
                service_name = self.attributes.get('servicename') or self.attributes.get('serviceName')
                service_code = self.attributes.get('servicecode') or self.attributes.get('serviceCode')

                # Prefer human-readable service name
                extracted_name = service_name or service_code
                if extracted_name:
                    self.name = extracted_name
                    print(f"MockShape extracted name from attributes: {self.name}")
                else:
                    self.name = 'Unknown Service'
                    print("MockShape could not extract name from attributes, using 'Unknown Service'")
            else:
                # If no attributes or empty attributes, and cost/latency are 0, likely a User
                cost = getattr(self, 'Cost', 0)
                latency = getattr(self, 'Latency', 0)
                if cost == 0 and latency == 0:
                    self.name = 'User'
                    print("MockShape has no attributes and zero cost/latency, assuming User")
                else:
                    self.name = 'Unknown Service'
                    print("MockShape has no attributes, using 'Unknown Service'")

class MockArrow:
    """Mock Arrow class to handle pickle deserialization"""
    def __init__(self, *args, **kwargs):
        self.Cost = 0
        self.Latency = 0
        self.attributes = {}
        self.start_item = None
        self.end_item = None
        self.isHeaded = True

        # Handle constructor arguments
        if len(args) >= 2:
            self.start_item = args[0]
            self.end_item = args[1]
        if len(args) >= 3:
            self.isHeaded = args[2]

        # Handle keyword arguments
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __setstate__(self, state):
        """Handle pickle state restoration"""
        self.__dict__.update(state)

        # Ensure required attributes exist
        if not hasattr(self, 'Cost'):
            self.Cost = 0
        if not hasattr(self, 'Latency'):
            self.Latency = 0
        if not hasattr(self, 'attributes'):
            self.attributes = {}

class MockGeneric:
    """Generic mock class for unknown objects"""
    def __init__(self, *args, **kwargs):
        # Store all arguments as attributes
        for i, arg in enumerate(args):
            setattr(self, f'arg_{i}', arg)
        for key, value in kwargs.items():
            setattr(self, key, value)

    def __setstate__(self, state):
        """Handle pickle state restoration"""
        self.__dict__.update(state)

class MapleGUIUnpickler(pickle.Unpickler):
    """Custom unpickler to handle MapleGUI classes"""

    def find_class(self, module, name):
        """Override find_class to handle missing MapleGUI classes"""
        print(f"Looking for class: {module}.{name}")

        # Map MapleGUI classes to our mock classes
        class_mapping = {
            'Shape': MockShape,
            'Arrow': MockArrow,
            'ModelShape': MockShape,  # Treat ModelShape as Shape
            'StorageShape': MockShape,  # Another shape variant
        }

        if name in class_mapping:
            print(f"Using mock class for {name}")
            return class_mapping[name]

        # For PyQt5/PySide classes, create a generic mock
        if module.startswith('PyQt5') or module.startswith('PySide') or module.startswith('qtpy'):
            print(f"Creating generic PyQt mock for {module}.{name}")
            return MockGeneric

        # For built-in types, use the standard mechanism
        if module in ['builtins', '__builtin__', '__main__']:
            try:
                return super().find_class(module, name)
            except (ImportError, AttributeError):
                print(f"Failed to find builtin class {module}.{name}, using generic")
                return MockGeneric

        # For other unknown modules/classes
        try:
            return super().find_class(module, name)
        except (ImportError, AttributeError, ModuleNotFoundError):
            print(f"Module {module} not found, creating generic class for {name}")
            return MockGeneric

@router.post("/load-pickle")
async def load_pickle_architecture(file: UploadFile = File(...)):
    """
    Load architecture from PyQt5 MapleGUI pickle file.

    This endpoint processes pickle files created by the PyQt5 MapleGUI application
    and converts them to a format compatible with the React frontend.
    """
    if not file.filename.endswith('.pkl'):
        raise HTTPException(status_code=400, detail="File must be a .pkl file")

    try:
        # Read the uploaded file
        content = await file.read()

        # Create a temporary file to store the pickle data
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pkl') as temp_file:
            temp_file.write(content)
            temp_file_path = temp_file.name

        try:
            # Load the pickle data using custom unpickler
            with open(temp_file_path, 'rb') as f:
                unpickler = MapleGUIUnpickler(f)
                canvas_data = unpickler.load()

            print(f"Successfully loaded pickle data. Keys: {list(canvas_data.keys()) if isinstance(canvas_data, dict) else 'Not a dict'}")
            print(f"Items count: {len(canvas_data.get('items', []))}")
            print(f"Adjacency matrix count: {len(canvas_data.get('adjacency_matrix', []))}")

            # Debug adjacency matrix structure
            if 'adjacency_matrix' in canvas_data and canvas_data['adjacency_matrix']:
                print("🔍 Original adjacency matrix structure:")
                for i, conn in enumerate(canvas_data['adjacency_matrix'][:3]):  # Show first 3 connections
                    print(f"  Connection {i}: {type(conn)} - {conn}")
                    if hasattr(conn, '__len__') and len(conn) >= 2:
                        print(f"    Source: {type(conn[0])} - {getattr(conn[0], 'name', 'No name')}")
                        print(f"    Target: {type(conn[1])} - {getattr(conn[1], 'name', 'No name')}")
            else:
                print("⚠️  No adjacency_matrix found in pickle data")
            # Convert the pickle data to JSON-serializable format
            converted_data = convert_pickle_to_json(canvas_data)

            # Restore VPC data to VPC service
            restore_vpc_data_to_service(canvas_data)

            print(f"Successfully converted data. Items: {len(converted_data.get('items', []))}, Connections: {len(converted_data.get('adjacency_matrix', []))}")

            # Debug: Print connection details
            if converted_data.get('adjacency_matrix'):
                print("🔍 Connection details:")
                for i, conn in enumerate(converted_data['adjacency_matrix']):
                    if conn and len(conn) >= 2:
                        source_ref, target_ref = conn[0], conn[1]
                        print(f"  Connection {i}: {source_ref} -> {target_ref}")
                    else:
                        print(f"  Connection {i}: Invalid format - {conn}")
            else:
                print("⚠️  No connections found in converted data")

            return JSONResponse(content=converted_data)

        except Exception as e:
            print(f"Error in pickle processing: {str(e)}")
            print(f"Error type: {type(e)}")
            import traceback
            traceback.print_exc()
            raise HTTPException(status_code=500, detail=f"Error processing pickle file: {str(e)}")

        finally:
            # Clean up the temporary file
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)

    except Exception as e:
        print(f"Outer error in load_pickle_architecture: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error processing pickle file: {str(e)}")

def convert_pickle_to_json(canvas_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Convert PyQt5 MapleGUI pickle data to JSON-serializable format.

    The pickle file contains PyQt5 objects that need to be converted to
    plain Python data structures for JSON serialization.
    """
    converted = {
        'items': [],
        'adjacency_matrix': [],
        'arch_type': canvas_data.get('arch_type', 'Unknown'),
        'mappings': canvas_data.get('mappings', {}),
        'vpcsquares': canvas_data.get('vpcsquares', {}),
        'vpclist': canvas_data.get('vpclist', []),
        'name': 'Loaded Architecture'
    }

    # Create object ID mapping for cross-references
    object_id_map = {}

    # Convert items and build object mapping
    if 'items' in canvas_data:
        print(f"Processing {len(canvas_data['items'])} items from canvas_data")
        for i, item_data in enumerate(canvas_data['items']):
            print(f"Processing item {i}: {type(item_data)}")
            if isinstance(item_data, dict):
                print(f"Item {i} keys: {list(item_data.keys())}")

            # Use original index if available, otherwise use current index
            original_index = item_data.get('original_index', i) if isinstance(item_data, dict) else i

            # Map original object to its index BEFORE conversion for adjacency matrix
            original_object = item_data.get('object') if isinstance(item_data, dict) else item_data
            if original_object:
                object_id_map[id(original_object)] = i  # Use current index for consistent mapping
                print(f"✅ Mapped object ID {id(original_object)} to index {i}")

                # Also try to map by object attributes for additional safety
                if hasattr(original_object, 'name'):
                    print(f"   Object {i} name: {getattr(original_object, 'name', 'Unknown')}")

            converted_item = convert_shape_item(item_data, original_index)
            if converted_item:
                converted['items'].append(converted_item)
                print(f"✅ Successfully converted item {i}")
            else:
                print(f"❌ Failed to convert item {i}")

        print(f"Final object_id_map: {object_id_map}")
        print(f"Total objects mapped: {len(object_id_map)}")
        print(f"Total items converted: {len(converted['items'])}")

    # Convert adjacency matrix (connections)
    if 'adjacency_matrix' in canvas_data:
        for i, connection in enumerate(canvas_data['adjacency_matrix']):
            converted_connection = convert_connection(connection, i, object_id_map)
            if converted_connection:
                converted['adjacency_matrix'].append(converted_connection)

    return converted

def convert_shape_item(item_data: Any, index: int) -> Dict[str, Any]:
    """
    Convert a Shape item from the pickle data to JSON format.

    Extracts relevant properties from PyQt5 Shape objects.
    """
    try:
        print(f"convert_shape_item called for item {index}, type: {type(item_data)}")

        # Handle different possible data structures
        if isinstance(item_data, dict):
            shape_object = item_data.get('object')
            pos = item_data.get('pos', (0, 0))
            item_cost = item_data.get('cost', 0)
            item_latency = item_data.get('latency', 0)
            item_attributes = item_data.get('attributes', {})

            # If the shape object doesn't have attributes but the item does, set them
            if shape_object and item_attributes and (not hasattr(shape_object, 'attributes') or not shape_object.attributes):
                shape_object.attributes = item_attributes
                print(f"Set attributes on shape object from item_data: {item_attributes}")

                # Also try to extract name from these attributes if the object doesn't have a proper name
                if not hasattr(shape_object, 'name') or not shape_object.name or shape_object.name in ['Unknown Service', 'CSP']:
                    service_name = item_attributes.get('servicename') or item_attributes.get('serviceName')
                    service_code = item_attributes.get('servicecode') or item_attributes.get('serviceCode')
                    extracted_name = service_name or service_code
                    if extracted_name:
                        shape_object.name = extracted_name
                        print(f"Updated MockShape object name from item attributes: {extracted_name}")
                    else:
                        print(f"No servicename/servicecode found in item attributes: {item_attributes}")

            # Handle case where item has empty/no attributes - likely a User node
            elif shape_object and (not item_attributes or item_attributes == '' or item_attributes == {}):
                if item_cost == 0 and item_latency == 0:
                    if not hasattr(shape_object, 'name') or not shape_object.name or shape_object.name == 'Unknown Service':
                        shape_object.name = 'User'
                        print(f"Set shape object name to 'User' based on empty attributes and zero cost/latency")
        else:
            # If item_data is the shape object itself
            shape_object = item_data
            pos = (0, 0)
            item_cost = 0
            item_latency = 0
            item_attributes = {}

        if not shape_object:
            print(f"No shape object found for item {index}")
            return None

        # Extract properties from the Shape object with detailed debugging
        name = getattr(shape_object, 'name', None)
        cost = getattr(shape_object, 'Cost', 0)
        latency = getattr(shape_object, 'Latency', 0)
        attributes = getattr(shape_object, 'attributes', {})
        text = getattr(shape_object, 'text', 'S')  # Provider type
        row_num = getattr(shape_object, 'RowNum', None)

        # Debug: Print all attributes of the shape object
        print(f"Shape object {index} attributes:")
        for attr_name in dir(shape_object):
            if not attr_name.startswith('_'):
                try:
                    attr_value = getattr(shape_object, attr_name)
                    if not callable(attr_value):
                        print(f"  {attr_name}: {attr_value}")
                except:
                    pass

        # PyQt5 MapleGUI stores the actual service name in the 'name' attribute
        # However, sometimes the name is generic (Service_0, Service_1) and the real info is in attributes
        print(f"Original name from pickle: '{name}'")

        # Check if the name is generic (Service_X pattern) - if so, use attributes
        import re
        is_generic_name = name and re.match(r'^Service_\d+$', str(name))

        if is_generic_name:
            print(f"Detected generic name '{name}', looking for real service name in attributes...")
        elif name and name not in ['Unknown Service', 'CSP', None]:
            print(f"Using original service name from pickle: '{name}'")
            # Keep the original name - this is what PyQt5 MapleGUI uses
        else:
            print("No valid name from pickle, trying alternative sources...")

        # Try to get service name from attributes (this is where MapleGUI stores the actual service names)
        if (is_generic_name or not name or name in ['Unknown Service', 'CSP', None]) and attributes and isinstance(attributes, dict):
            # Primary source: servicename (e.g., "AWS Lambda", "Amazon Simple Storage Service") - human readable
            service_name = attributes.get('servicename') or attributes.get('serviceName') or attributes.get('service_name')
            # Secondary source: servicecode (e.g., "AWSLambda", "AmazonS3", "AmazonQuickSight") - code format
            service_code = attributes.get('servicecode') or attributes.get('serviceCode') or attributes.get('service_code')

            # Prefer servicename (human readable) over servicecode, but use either if available
            service_name_from_attrs = service_name or service_code

            if service_name_from_attrs and service_name_from_attrs not in ['S', 'CSP', 'Unknown Service']:
                name = service_name_from_attrs
                print(f"Found service name '{name}' in attributes (servicename: '{service_name}', servicecode: '{service_code}')")

        # Check if the object has servicecode or servicename directly as attributes (not in attributes dict)
        if not name or name in ['Unknown Service', 'CSP']:
            # Try to get servicecode/servicename directly from object attributes
            direct_service_code = getattr(shape_object, 'servicecode', None) or getattr(shape_object, 'serviceCode', None)
            direct_service_name = getattr(shape_object, 'servicename', None) or getattr(shape_object, 'serviceName', None)

            service_from_direct = direct_service_code or direct_service_name
            if service_from_direct and service_from_direct not in ['S', 'CSP', 'Unknown Service']:
                name = service_from_direct
                print(f"Found service name '{name}' in direct object attributes")

        # Special case: if text is "User" or similar, use that as the name
        if text and text.lower() in ['user', 'client', 'customer'] and (not name or name in ['CSP', 'Unknown Service']):
            name = text
            print(f"Using text '{name}' as service name for user/client")

        # Special case: if attributes are empty or missing and cost/latency are 0, likely a User node
        if (not name or name in ['Unknown Service', 'CSP'] or is_generic_name) and (
            not attributes or attributes == '' or attributes == {} or
            (isinstance(attributes, dict) and not any(attributes.values()))
        ) and cost == 0 and latency == 0:
            name = 'User'
            print(f"Detected User node based on empty attributes and zero cost/latency")

        # If no name from attributes, try different possible name attributes
        if not name or name in ['Unknown Service', 'CSP']:
            # Try alternative name attributes
            possible_names = ['name', 'serviceName', 'service_name', 'text', 'label', 'title', 'fileName']
            for name_attr in possible_names:
                potential_name = getattr(shape_object, name_attr, None)
                if potential_name and potential_name not in ['S', 'CSP', 'Unknown Service']:
                    # If it's a fileName, extract service name from it
                    if name_attr == 'fileName' and isinstance(potential_name, str):
                        if potential_name.endswith('.json'):
                            potential_name = potential_name[:-5]  # Remove .json extension
                    name = potential_name
                    print(f"Found name '{name}' in attribute '{name_attr}'")
                    break

        # Try to extract service information from RowNum if available
        if (not name or name in ['Unknown Service', 'CSP']) and row_num is not None:
            # RowNum might reference a row in the Excel sheet - try to map it to a service
            print(f"Attempting to map RowNum {row_num} to a service name")
            try:
                # Try to read from the Excel sheet to get the actual service name
                import pandas as pd
                import os

                # Path to the Excel file (adjust as needed)
                excel_path = os.path.join(os.path.dirname(__file__), '..', '..', '..', 'MapleGUI', 'CS-arch.xlsx')
                if os.path.exists(excel_path):
                    df = pd.read_excel(excel_path)
                    if row_num < len(df):
                        # Try to get service name from the Excel row
                        row_data = df.iloc[row_num]
                        # Look for service name in common columns
                        for col in ['Service', 'ServiceName', 'service', 'servicename', 'Name', 'name']:
                            if col in row_data and pd.notna(row_data[col]):
                                potential_service = str(row_data[col]).strip()
                                if potential_service and potential_service not in ['S', 'CSP', 'Unknown Service']:
                                    name = potential_service
                                    print(f"Found service name '{name}' from Excel row {row_num}")
                                    break
                else:
                    print(f"Excel file not found at {excel_path}")
            except Exception as e:
                print(f"Error reading Excel file: {e}")
                # Fallback for specific known cases
                if row_num == 234:
                    name = "AmazonQuickSight"
                    print(f"Using fallback mapping: RowNum {row_num} -> '{name}'")

        # If still no name, try to extract from object's __dict__
        if not name or name in ['Unknown Service', 'CSP']:
            obj_dict = getattr(shape_object, '__dict__', {})
            print(f"Object __dict__: {obj_dict}")
            for key, value in obj_dict.items():
                if 'name' in key.lower() and isinstance(value, str) and value not in ['S', 'CSP', 'Unknown Service', '']:
                    name = value
                    print(f"Found name '{name}' in __dict__ key '{key}'")
                    break

        # Position was already extracted above based on data structure

        # Ensure we have a valid name
        final_name = str(name) if name and name not in ['Unknown Service', 'CSP'] else f'Service_{index}'
        print(f"Final name for object {index}: {final_name}")

        # Use object cost/latency if available, otherwise use item-level values
        final_cost = convert_numeric_value(cost) if cost != 0 else convert_numeric_value(item_cost)
        final_latency = convert_numeric_value(latency) if latency != 0 else convert_numeric_value(item_latency)
        final_attributes = convert_attributes(attributes) if attributes else convert_attributes(item_attributes)

        # Get original React ID if available for better mapping
        original_react_id = None
        if isinstance(item_data, dict):
            original_react_id = item_data.get('original_react_id')

        return {
            'object': {
                'name': final_name,
                'Cost': final_cost,
                'Latency': final_latency,
                'attributes': final_attributes,
                'text': str(text) if text else 'S',
                'RowNum': row_num,
                'id': index,  # Use the original index for consistent mapping
                'original_react_id': original_react_id  # Store original React Flow ID
            },
            'pos': [float(pos[0]) if pos[0] is not None else 0,
                   float(pos[1]) if pos[1] is not None else 0],
            'cost': final_cost,
            'latency': final_latency,
            'attributes': final_attributes
        }

    except Exception as e:
        print(f"Error converting shape item: {e}")
        return None

def convert_connection(connection: Tuple[Any, Any, Any], index: int, object_id_map: Dict[int, int]) -> List[Any]:
    """
    Convert a connection from the adjacency matrix to JSON format.

    Connections in MapleGUI are stored as tuples of (source, target, connection_data).
    """
    try:
        if len(connection) < 2:
            print(f"Connection {index}: Invalid connection format - less than 2 elements")
            return None

        source_obj = connection[0]
        target_obj = connection[1]
        connection_data = connection[2] if len(connection) > 2 else None

        # Create simplified references to the objects using the mapping
        source_obj_id = id(source_obj)
        target_obj_id = id(target_obj)

        print(f"Connection {index}: Processing source_obj_id={source_obj_id}, target_obj_id={target_obj_id}")
        print(f"Connection {index}: Available object IDs in map: {list(object_id_map.keys())}")

        # Get mapped IDs with better error handling
        source_mapped_id = object_id_map.get(source_obj_id)
        target_mapped_id = object_id_map.get(target_obj_id)

        if source_mapped_id is None:
            print(f"⚠️  Connection {index}: Source object ID {source_obj_id} not found in object_id_map")
            # Try to find by object attributes as fallback
            source_name = getattr(source_obj, 'name', f'Source_{index}')
            print(f"Connection {index}: Source object name: {source_name}")
            # Use index as fallback - this might work if objects are in order
            source_mapped_id = index * 2  # Rough fallback

        if target_mapped_id is None:
            print(f"⚠️  Connection {index}: Target object ID {target_obj_id} not found in object_id_map")
            # Try to find by object attributes as fallback
            target_name = getattr(target_obj, 'name', f'Target_{index}')
            print(f"Connection {index}: Target object name: {target_name}")
            # Use index as fallback - this might work if objects are in order
            target_mapped_id = index * 2 + 1  # Rough fallback

        source_ref = {
            'name': getattr(source_obj, 'name', f'Source_{index}'),
            'id': source_mapped_id
        }

        target_ref = {
            'name': getattr(target_obj, 'name', f'Target_{index}'),
            'id': target_mapped_id
        }

        print(f"Connection {index}: source_ref={source_ref}, target_ref={target_ref}")

        # Convert connection data if present
        conn_data = None
        if connection_data:
            if isinstance(connection_data, tuple) and len(connection_data) >= 2:
                conn_data = {
                    'cost': convert_numeric_value(connection_data[0]),
                    'latency': convert_numeric_value(connection_data[1]),
                    'attributes': convert_attributes(connection_data[2]) if len(connection_data) > 2 else {}
                }
            else:
                conn_data = {
                    'cost': 0,
                    'latency': 0,
                    'attributes': {}
                }

        print(f"✅ Connection {index}: Successfully converted {source_ref['name']} -> {target_ref['name']}")
        return [source_ref, target_ref, conn_data]

    except Exception as e:
        print(f"❌ Error converting connection {index}: {e}")
        import traceback
        traceback.print_exc()
        return None

def convert_numeric_value(value: Any) -> float:
    """
    Convert various numeric representations to float.

    Handles strings like "N/A", None values, and numeric types.
    """
    if value is None:
        return 0.0

    if isinstance(value, (int, float)):
        return float(value)

    if isinstance(value, str):
        # Handle common string representations
        value = value.strip()
        if value.lower() in ['n/a', 'na', 'none', '', '--']:
            return 0.0

        try:
            # Try to extract numeric value from string
            import re
            numeric_match = re.search(r'[\d.]+', value)
            if numeric_match:
                return float(numeric_match.group())
            return 0.0
        except:
            return 0.0

    return 0.0

def convert_attributes(attributes: Any) -> Dict[str, Any]:
    """
    Convert attributes to JSON-serializable format.

    Handles various attribute formats from PyQt5 objects.
    """
    if not attributes:
        return {}

    if isinstance(attributes, dict):
        converted = {}
        for key, value in attributes.items():
            # Convert key to string
            str_key = str(key) if key is not None else 'unknown'

            # Convert value to JSON-serializable format
            if isinstance(value, (str, int, float, bool)):
                converted[str_key] = value
            elif value is None:
                converted[str_key] = None
            else:
                # Convert complex objects to string representation
                converted[str_key] = str(value)

        return converted

    elif isinstance(attributes, str):
        # If attributes is a string, try to parse as JSON
        try:
            return json.loads(attributes)
        except:
            return {'raw': attributes}

    else:
        # Convert other types to string
        return {'raw': str(attributes)}

@router.post("/save-to-database")
async def save_architecture_to_database(
    architecture_data: Dict[str, Any],
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Save architecture data to database for the authenticated user.

    This endpoint stores the architecture in the database with proper user association,
    enabling cloud storage and sharing capabilities.
    """
    try:
        # Extract architecture components
        name = architecture_data.get('name', 'Untitled Architecture')
        nodes = architecture_data.get('nodes', [])
        edges = architecture_data.get('edges', [])
        metadata = architecture_data.get('metadata', {})

        # Calculate costs if available
        total_cost = 0.0
        total_latency = 0.0

        if nodes:
            total_cost = sum(node.get('data', {}).get('cost', 0) for node in nodes)
            total_latency = max((node.get('data', {}).get('latency', 0) for node in nodes), default=0)

        # Check if architecture with same name exists for this user
        existing_arch = db.query(Architecture).filter(
            Architecture.owner_id == current_user.id,
            Architecture.name == name
        ).first()

        if existing_arch:
            # Update existing architecture
            existing_arch.nodes_data = nodes
            existing_arch.edges_data = edges
            existing_arch.arch_metadata = metadata
            existing_arch.total_cost = total_cost
            existing_arch.total_latency = total_latency
            existing_arch.updated_at = datetime.utcnow()
            existing_arch.version += 1

            db.commit()
            db.refresh(existing_arch)

            return JSONResponse(
                content={
                    'success': True,
                    'message': f'Architecture "{name}" updated successfully',
                    'architecture_id': existing_arch.id,
                    'version': existing_arch.version,
                    'action': 'updated'
                }
            )
        else:
            # Create new architecture
            new_architecture = Architecture(
                name=name,
                description=metadata.get('description', ''),
                owner_id=current_user.id,
                nodes_data=nodes,
                edges_data=edges,
                arch_metadata=metadata,
                total_cost=total_cost,
                total_latency=total_latency,
                architecture_type=metadata.get('archType', 'Microservices'),
                version=1
            )

            db.add(new_architecture)
            db.commit()
            db.refresh(new_architecture)

            return JSONResponse(
                content={
                    'success': True,
                    'message': f'Architecture "{name}" saved successfully',
                    'architecture_id': new_architecture.id,
                    'version': new_architecture.version,
                    'action': 'created'
                }
            )

    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error saving architecture to database: {str(e)}")


@router.get("/list")
async def list_user_architectures(
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    List all architectures for the authenticated user.
    """
    try:
        architectures = db.query(Architecture).filter(
            Architecture.owner_id == current_user.id
        ).order_by(Architecture.updated_at.desc()).all()

        architecture_list = []
        for arch in architectures:
            architecture_list.append({
                'id': arch.id,
                'name': arch.name,
                'description': arch.description,
                'architecture_type': arch.architecture_type,
                'total_cost': arch.total_cost,
                'total_latency': arch.total_latency,
                'version': arch.version,
                'created_at': arch.created_at.isoformat(),
                'updated_at': arch.updated_at.isoformat(),
                'node_count': len(arch.nodes_data) if arch.nodes_data else 0,
                'edge_count': len(arch.edges_data) if arch.edges_data else 0
            })

        return JSONResponse(
            content={
                'success': True,
                'architectures': architecture_list,
                'count': len(architecture_list)
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error listing architectures: {str(e)}")


@router.get("/load-from-database/{architecture_id}")
async def load_architecture_from_database(
    architecture_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Load a specific architecture from database for the authenticated user.
    """
    try:
        architecture = db.query(Architecture).filter(
            Architecture.id == architecture_id,
            Architecture.owner_id == current_user.id
        ).first()

        if not architecture:
            raise HTTPException(status_code=404, detail="Architecture not found")

        return JSONResponse(
            content={
                'success': True,
                'architecture': {
                    'id': architecture.id,
                    'name': architecture.name,
                    'description': architecture.description,
                    'nodes': architecture.nodes_data,
                    'edges': architecture.edges_data,
                    'metadata': architecture.arch_metadata or {},
                    'architecture_type': architecture.architecture_type,
                    'total_cost': architecture.total_cost,
                    'total_latency': architecture.total_latency,
                    'version': architecture.version,
                    'created_at': architecture.created_at.isoformat(),
                    'updated_at': architecture.updated_at.isoformat()
                }
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error loading architecture: {str(e)}")


@router.delete("/delete-from-database/{architecture_id}")
async def delete_architecture_from_database(
    architecture_id: int,
    current_user: User = Depends(get_current_user),
    db: Session = Depends(get_db)
):
    """
    Delete a specific architecture from database for the authenticated user.
    """
    try:
        architecture = db.query(Architecture).filter(
            Architecture.id == architecture_id,
            Architecture.owner_id == current_user.id
        ).first()

        if not architecture:
            raise HTTPException(status_code=404, detail="Architecture not found")

        architecture_name = architecture.name
        db.delete(architecture)
        db.commit()

        return JSONResponse(
            content={
                'success': True,
                'message': f'Architecture "{architecture_name}" deleted successfully'
            }
        )

    except HTTPException:
        raise
    except Exception as e:
        db.rollback()
        raise HTTPException(status_code=500, detail=f"Error deleting architecture: {str(e)}")


@router.post("/export-json")
async def export_architecture_json(architecture_data: Dict[str, Any]):
    """
    Export architecture data to JSON format for download.

    This endpoint prepares the current architecture state as a JSON file
    that can be downloaded by the user.
    """
    try:
        # Add metadata
        architecture_data['exported_at'] = str(datetime.utcnow())
        architecture_data['version'] = '1.0'

        # Convert to JSON string
        json_data = json.dumps(architecture_data, indent=2)

        return JSONResponse(
            content={'success': True, 'data': json_data},
            headers={
                'Content-Disposition': 'attachment; filename="architecture.json"',
                'Content-Type': 'application/json'
            }
        )

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error exporting architecture: {str(e)}")


# PyQt5-compatible classes for pickle serialization
class PyQt5Shape:
    """
    PyQt5-compatible Shape class for pickle serialization.

    This class replicates the exact structure and attributes of the PyQt5 Shape class
    to ensure complete compatibility with MapleGUI pickle files.
    """
    def __init__(self, text, a, b, cost, latency, excelrownum):
        # Core shape properties
        self.text = text
        self.a = a  # width
        self.b = b  # height
        self.Cost = cost
        self.Latency = latency
        self.RowNum = excelrownum

        # Service identification
        self.name = text  # Service name (e.g., "Amazon EC2")
        self.servicecode = None  # Will be set based on service mapping
        self.servicename = None  # Will be set based on service mapping

        # Position and visual properties
        self.Type1 = None
        self.CostDesc = ""
        self.LatencyDesc = ""
        self.group = None
        self.CloudLatency = 0.0

        # Configuration and attributes
        self.attributes = {}
        self.paramList = None
        self.compareList = None
        self.workLoad = None
        self.region = "ap-south-1"

        # Connection tracking
        self.in_arrows = []
        self.out_arrows = []

        # Additional PyQt5 properties
        self.selectedTime = None
        self.tfrs = []
        self.perfAttributes = {}

        # Position methods compatibility
        self._x = 0
        self._y = 0

    def x(self):
        """Return x coordinate"""
        return self._x

    def y(self):
        """Return y coordinate"""
        return self._y

    def setPos(self, x, y):
        """Set position"""
        self._x = x
        self._y = y

    def addInLink(self, arrow):
        """Add incoming arrow"""
        if arrow not in self.in_arrows:
            self.in_arrows.append(arrow)

    def addOutLink(self, arrow):
        """Add outgoing arrow"""
        if arrow not in self.out_arrows:
            self.out_arrows.append(arrow)


class PyQt5Arrow:
    """
    PyQt5-compatible Arrow class for pickle serialization.

    This class replicates the exact structure and attributes of the PyQt5 Arrow class
    to ensure complete compatibility with MapleGUI pickle files.
    """
    def __init__(self, start_item, end_item, isHeaded=True):
        self.start_item = start_item
        self.end_item = end_item
        self.isHeaded = isHeaded

        # Cost and performance properties
        self.Cost = 0
        self.Latency = 0
        self.attributes = {}

        # Add this arrow to the connected shapes
        if start_item:
            start_item.addOutLink(self)
        if end_item:
            end_item.addInLink(self)


# Service mapping from React Flow to PyQt5 MapleGUI format
REACT_TO_MAPLEGUI_SERVICE_MAPPING = {
    'amazon-ec2': 'Amazon EC2',
    'aws-s3': 'Amazon Simple Storage System (S3)',
    'aws-dynamodb': 'Amazon DynamoDB',
    'aws-lambda': 'AWS Lambda',
    'aws-rds': 'Amazon RDS',
    'aws-elasticache': 'Amazon ElastiCache',
    'aws-cloudfront': 'Amazon CloudFront',
    'aws-api-gateway': 'Amazon API Gateway',
    'aws-sns': 'Amazon SNS',
    'aws-sqs': 'Amazon SQS',
    'aws-kinesis': 'Amazon Kinesis',
    'aws-redshift': 'Amazon Redshift',
    'aws-elasticsearch': 'Amazon Elasticsearch Service',
    'aws-ecs': 'Amazon ECS',
    'aws-eks': 'Amazon EKS',
    'aws-fargate': 'AWS Fargate',
    'aws-batch': 'AWS Batch',
    'aws-step-functions': 'AWS Step Functions',
    'aws-eventbridge': 'Amazon EventBridge',
    'aws-cognito': 'Amazon Cognito',
    'aws-iam': 'AWS IAM',
    'aws-cloudwatch': 'Amazon CloudWatch',
    'aws-cloudtrail': 'AWS CloudTrail',
    'aws-config': 'AWS Config',
    'aws-secrets-manager': 'AWS Secrets Manager',
    'aws-systems-manager': 'AWS Systems Manager',
    'aws-vpc': 'Amazon VPC',
    'aws-route53': 'Amazon Route 53',
    'aws-elb': 'Elastic Load Balancing',
    'aws-auto-scaling': 'AWS Auto Scaling',
    'aws-cloudformation': 'AWS CloudFormation',
    'aws-codepipeline': 'AWS CodePipeline',
    'aws-codebuild': 'AWS CodeBuild',
    'aws-codecommit': 'AWS CodeCommit',
    'aws-codedeploy': 'AWS CodeDeploy',
    'user': 'User'
}

# Service code mapping for PyQt5 compatibility
SERVICE_CODE_MAPPING = {
    'Amazon EC2': 'AmazonEC2',
    'Amazon Simple Storage System (S3)': 'AmazonS3',
    'Amazon DynamoDB': 'AmazonDynamoDB',
    'AWS Lambda': 'AWSLambda',
    'Amazon RDS': 'AmazonRDS',
    'Amazon ElastiCache': 'AmazonElastiCache',
    'Amazon CloudFront': 'AmazonCloudFront',
    'Amazon API Gateway': 'AmazonAPIGateway',
    'Amazon SNS': 'AmazonSNS',
    'Amazon SQS': 'AmazonSQS',
    'Amazon Kinesis': 'AmazonKinesis',
    'Amazon Redshift': 'AmazonRedshift',
    'Amazon Elasticsearch Service': 'AmazonElasticsearch',
    'Amazon ECS': 'AmazonECS',
    'Amazon EKS': 'AmazonEKS',
    'AWS Fargate': 'AWSFargate',
    'AWS Batch': 'AWSBatch',
    'AWS Step Functions': 'AWSStepFunctions',
    'Amazon EventBridge': 'AmazonEventBridge',
    'Amazon Cognito': 'AmazonCognito',
    'AWS IAM': 'AWSIAM',
    'Amazon CloudWatch': 'AmazonCloudWatch',
    'AWS CloudTrail': 'AWSCloudTrail',
    'AWS Config': 'AWSConfig',
    'AWS Secrets Manager': 'AWSSecretsManager',
    'AWS Systems Manager': 'AWSSystemsManager',
    'Amazon VPC': 'AmazonVPC',
    'Amazon Route 53': 'AmazonRoute53',
    'Elastic Load Balancing': 'ElasticLoadBalancing',
    'AWS Auto Scaling': 'AWSAutoScaling',
    'AWS CloudFormation': 'AWSCloudFormation',
    'AWS CodePipeline': 'AWSCodePipeline',
    'AWS CodeBuild': 'AWSCodeBuild',
    'AWS CodeCommit': 'AWSCodeCommit',
    'AWS CodeDeploy': 'AWSCodeDeploy',
    'User': 'User'
}


def convert_react_node_to_pyqt5_shape(node: Dict[str, Any]) -> PyQt5Shape:
    """
    Convert a React Flow node to a PyQt5-compatible Shape object.

    Args:
        node: React Flow node data

    Returns:
        PyQt5Shape: Compatible shape object for pickle serialization
    """
    # Extract service information
    service_data = node.get('data', {}).get('service', {})
    service_id = service_data.get('id', 'unknown')
    service_name = service_data.get('name', 'Unknown Service')

    # Map React service ID to PyQt5 service name
    maplegui_service_name = REACT_TO_MAPLEGUI_SERVICE_MAPPING.get(service_id, service_name)

    # Extract position
    position = node.get('position', {'x': 0, 'y': 0})

    # Extract cost and latency
    cost = node.get('data', {}).get('cost', 0)
    latency = node.get('data', {}).get('latency', 0)

    # Create PyQt5 Shape
    shape = PyQt5Shape(
        text=maplegui_service_name,
        a=200,  # Default width
        b=100,  # Default height
        cost=cost,
        latency=latency,
        excelrownum=None
    )

    # Set position
    shape.setPos(position['x'], position['y'])

    # Set service identification
    shape.name = maplegui_service_name
    shape.servicecode = SERVICE_CODE_MAPPING.get(maplegui_service_name, 'Unknown')
    shape.servicename = maplegui_service_name

    # Convert configuration to attributes
    config = node.get('data', {}).get('config', {})
    shape.attributes = convert_react_config_to_pyqt5_attributes(config, service_id)

    # Set performance attributes for ML compatibility
    shape.perfAttributes = config.copy()

    return shape


def convert_react_config_to_pyqt5_attributes(config: Dict[str, Any], service_id: str) -> Dict[str, Any]:
    """
    Convert React Flow service configuration to PyQt5 attributes format.

    Args:
        config: React Flow service configuration
        service_id: Service identifier

    Returns:
        Dict: PyQt5-compatible attributes
    """
    # Start with the config as base
    attributes = config.copy()

    # Add service-specific attribute mappings
    if service_id == 'amazon-ec2':
        # Map EC2-specific attributes
        if 'instanceType' in config:
            attributes['instanceType'] = config['instanceType']
        if 'region' in config:
            attributes['region'] = config['region']
    elif service_id == 'aws-dynamodb':
        # Map DynamoDB-specific attributes
        if 'readCapacity' in config:
            attributes['readCapacity'] = config['readCapacity']
        if 'writeCapacity' in config:
            attributes['writeCapacity'] = config['writeCapacity']
    elif service_id == 'aws-lambda':
        # Map Lambda-specific attributes
        if 'memory' in config:
            attributes['memory'] = config['memory']
        if 'runtime' in config:
            attributes['runtime'] = config['runtime']

    return attributes


def convert_react_edge_to_pyqt5_arrow(edge: Dict[str, Any], shape_map: Dict[str, PyQt5Shape]) -> PyQt5Arrow:
    """
    Convert a React Flow edge to a PyQt5-compatible Arrow object.

    Args:
        edge: React Flow edge data
        shape_map: Mapping of node IDs to PyQt5Shape objects

    Returns:
        PyQt5Arrow: Compatible arrow object for pickle serialization
    """
    source_id = edge.get('source')
    target_id = edge.get('target')
    edge_id = edge.get('id', 'unknown')

    # Validate edge data
    if not source_id:
        raise ValueError(f"Edge {edge_id} missing source ID")
    if not target_id:
        raise ValueError(f"Edge {edge_id} missing target ID")

    start_item = shape_map.get(source_id)
    end_item = shape_map.get(target_id)

    if not start_item:
        available_sources = list(shape_map.keys())
        raise ValueError(f"Could not find source shape for edge {edge_id}: source='{source_id}' not in {available_sources}")

    if not end_item:
        available_targets = list(shape_map.keys())
        raise ValueError(f"Could not find target shape for edge {edge_id}: target='{target_id}' not in {available_targets}")

    # Create PyQt5 Arrow
    arrow = PyQt5Arrow(start_item, end_item, isHeaded=True)

    # Set cost and latency from edge data
    edge_data = edge.get('data', {})
    arrow.Cost = float(edge_data.get('cost', 0)) if edge_data.get('cost') is not None else 0.0
    arrow.Latency = float(edge_data.get('latency', 0)) if edge_data.get('latency') is not None else 0.0

    # Set attributes - preserve all edge information for better debugging
    arrow.attributes = {
        'bandwidth': str(edge_data.get('bandwidth', '')),
        'protocol': str(edge_data.get('protocol', 'TCP')),
        'source_handle': edge.get('sourceHandle'),
        'target_handle': edge.get('targetHandle'),
        'edge_type': edge.get('type', 'default'),
        'animated': bool(edge.get('animated', False)),
        'edge_id': edge_id,  # Store original edge ID for debugging
        'style': edge.get('style', {}),  # Preserve visual styling
        'markerEnd': edge.get('markerEnd', 'arrowclosed')
    }

    print(f"Created arrow: {start_item.name} -> {end_item.name} (cost: {arrow.Cost}, latency: {arrow.Latency})")
    return arrow


@router.post("/export-pickle")
async def export_architecture_pickle(architecture_data: Dict[str, Any]):
    """
    Export architecture data to PyQt5 MapleGUI-compatible pickle format for download.

    This endpoint converts React Flow architecture data to the exact pickle format
    used by PyQt5 MapleGUI, ensuring complete bidirectional compatibility.

    Args:
        architecture_data: React Flow architecture data containing nodes, edges, and metadata

    Returns:
        Downloadable .pkl file compatible with PyQt5 MapleGUI
    """
    try:
        print("Received architecture data for pickle save:")
        print(f"Nodes: {len(architecture_data.get('nodes', []))}")
        print(f"Edges: {len(architecture_data.get('edges', []))}")

        # Extract data from request
        nodes = architecture_data.get('nodes', [])
        edges = architecture_data.get('edges', [])
        metadata = architecture_data.get('metadata', {})

        # Convert React Flow nodes to PyQt5 Shape objects
        shape_map = {}  # Map node IDs to Shape objects
        items = []

        for i, node in enumerate(nodes):
            try:
                shape = convert_react_node_to_pyqt5_shape(node)
                shape_map[node['id']] = shape

                # Store original React Flow node ID in the shape for later reference
                shape.original_react_id = node['id']
                shape.original_index = i  # Store the original order index

                # Create item data in PyQt5 format
                item_data = {
                    'object': shape,
                    'pos': (shape.x(), shape.y()),
                    'cost': shape.Cost,
                    'latency': shape.Latency,
                    'attributes': shape.attributes,
                    'original_react_id': node['id'],  # Store original ID for mapping
                    'original_index': i  # Store original order
                }
                items.append(item_data)
                print(f"Converted node {node['id']} (index {i}) to shape {shape.name}")

            except Exception as e:
                print(f"Error converting node {node.get('id', 'unknown')}: {str(e)}")
                raise HTTPException(status_code=400, detail=f"Error converting node: {str(e)}")

        # Convert React Flow edges to PyQt5 Arrow objects and adjacency matrix
        adjacency_matrix = []
        edge_conversion_errors = []

        print(f"Converting {len(edges)} edges to PyQt5 format...")
        for i, edge in enumerate(edges):
            try:
                # Validate edge structure
                if not edge.get('source') or not edge.get('target'):
                    print(f"⚠️  Edge {i} missing source or target: {edge}")
                    edge_conversion_errors.append(f"Edge {i}: Missing source or target")
                    continue

                # Check if source and target nodes exist in shape_map
                source_id = edge.get('source')
                target_id = edge.get('target')

                if source_id not in shape_map:
                    print(f"⚠️  Edge {i}: Source node '{source_id}' not found in shape_map")
                    edge_conversion_errors.append(f"Edge {i}: Source node '{source_id}' not found")
                    continue

                if target_id not in shape_map:
                    print(f"⚠️  Edge {i}: Target node '{target_id}' not found in shape_map")
                    edge_conversion_errors.append(f"Edge {i}: Target node '{target_id}' not found")
                    continue

                # Convert edge to arrow
                arrow = convert_react_edge_to_pyqt5_arrow(edge, shape_map)

                # Create adjacency matrix entry in PyQt5 format: (start_shape, end_shape, (cost, latency, attributes))
                adjacency_entry = (
                    arrow.start_item,
                    arrow.end_item,
                    (arrow.Cost, arrow.Latency, arrow.attributes)
                )
                adjacency_matrix.append(adjacency_entry)
                print(f"✅ Converted edge {edge.get('id', f'edge_{i}')} to arrow {arrow.start_item.name} -> {arrow.end_item.name}")

            except Exception as e:
                error_msg = f"Error converting edge {edge.get('id', f'edge_{i}')}: {str(e)}"
                print(f"❌ {error_msg}")
                edge_conversion_errors.append(error_msg)
                # Continue processing other edges instead of failing completely
                continue

        print(f"Edge conversion completed: {len(adjacency_matrix)} successful, {len(edge_conversion_errors)} failed")

        # Log conversion errors but don't fail the entire export
        if edge_conversion_errors:
            print("⚠️  Edge conversion errors:")
            for error in edge_conversion_errors:
                print(f"   - {error}")
            # You might want to include this in the response for debugging
            print(f"Proceeding with {len(adjacency_matrix)} valid edges out of {len(edges)} total edges")

        # Create PyQt5 MapleGUI canvas data structure
        # Get VPC data from VPC service
        try:
            from app.services.vpc_service import vpc_service
            vpc_list_response = vpc_service.list_vpcs()

            # Convert VPC data to PyQt5 format
            vpclist = {}
            vpcsquares = {}

            for vpc in vpc_list_response.vpcs:
                vpc_id = vpc.vpc_id
                # Convert to PyQt5 vpclist format
                vpclist[vpc_id] = {
                    "connections": vpc.config.connections,
                    "hours": vpc.config.hours_per_day,
                    "NAT Gateways": vpc.config.nat_gateways,
                    "Data processed per NAT Gateway": f"{vpc.config.nat_data_gb_per_month} GB/month",
                    "No. of In-use public IPV4 addresses": vpc.config.inuse_public_ipv4,
                    "No. of Idle public IPV4 addresses": vpc.config.idle_public_ipv4,
                    "COST": f"{vpc.config.cost} USD"
                }

                # Create vpcsquares entry (service mappings)
                vpcsquares[vpc_id] = [(service_id, None) for service_id in vpc.service_ids]

        except Exception as e:
            print(f"Error getting VPC data: {e}")
            vpclist = {}
            vpcsquares = {}

        canvas_data = {
            'items': items,
            'adjacency_matrix': adjacency_matrix,
            'arch_type': metadata.get('archType', 'Microservices'),
            'mapping': {},  # Empty mapping for now
            'vpcsquares': vpcsquares,  # VPC service mappings
            'vpclist': vpclist  # VPC configurations
        }

        print(f"Created canvas data with {len(items)} items and {len(adjacency_matrix)} connections")

        # Create temporary file for pickle data
        with tempfile.NamedTemporaryFile(delete=False, suffix='.pkl') as temp_file:
            # Serialize to pickle format
            pickle.dump(canvas_data, temp_file)
            temp_file_path = temp_file.name

        try:
            # Read the pickle file content
            with open(temp_file_path, 'rb') as f:
                pickle_content = f.read()

            # Clean up temporary file
            os.unlink(temp_file_path)

            # Generate filename
            arch_name = architecture_data.get('name', 'architecture')
            safe_name = "".join(c for c in arch_name if c.isalnum() or c in (' ', '-', '_')).rstrip()
            filename = f"{safe_name}.pkl"

            print(f"Successfully created pickle file: {filename}")

            # Return the pickle file as downloadable content
            return Response(
                content=pickle_content,
                media_type='application/octet-stream',
                headers={
                    'Content-Disposition': f'attachment; filename="{filename}"',
                    'Content-Type': 'application/octet-stream'
                }
            )

        except Exception as e:
            # Clean up temporary file on error
            if os.path.exists(temp_file_path):
                os.unlink(temp_file_path)
            raise e

    except HTTPException:
        # Re-raise HTTP exceptions
        raise
    except Exception as e:
        print(f"Unexpected error in save_architecture_pickle: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Error creating pickle file: {str(e)}")


def restore_vpc_data_to_service(canvas_data: Dict[str, Any]):
    """
    Restore VPC data from PyQt5 pickle file to the VPC service.

    This function converts PyQt5 VPC data back to the VPC service format,
    maintaining compatibility between the web application and PyQt5 MapleGUI.
    """
    try:
        from app.services.vpc_service import vpc_service
        from app.models.requests import VPCConfigRequest, VPCCreateRequest

        # Clear existing VPCs
        vpc_service.clear_all_vpcs()

        vpclist = canvas_data.get('vpclist', {})
        vpcsquares = canvas_data.get('vpcsquares', {})

        print(f"Restoring VPC data: {len(vpclist)} VPCs found")

        for vpc_id_str, vpc_config in vpclist.items():
            try:
                vpc_id = int(vpc_id_str)

                # Extract VPC configuration from PyQt5 format
                connections = vpc_config.get('connections', 1)
                hours = vpc_config.get('hours', 24.0)
                nat_gateways = vpc_config.get('NAT Gateways', 1)

                # Parse data processing value (format: "X GB/month")
                nat_data_str = vpc_config.get('Data processed per NAT Gateway', '1 GB/month')
                nat_data_gb = 1.0
                if isinstance(nat_data_str, str) and 'GB/month' in nat_data_str:
                    try:
                        nat_data_gb = float(nat_data_str.split(' ')[0])
                    except (ValueError, IndexError):
                        nat_data_gb = 1.0

                inuse_ipv4 = vpc_config.get('No. of In-use public IPV4 addresses', 1)
                idle_ipv4 = vpc_config.get('No. of Idle public IPV4 addresses', 1)

                # Get service IDs for this VPC
                service_ids = []
                if vpc_id in vpcsquares:
                    service_ids = [item[0] for item in vpcsquares[vpc_id] if item[0]]

                # Create VPC configuration
                config = VPCConfigRequest(
                    connections=int(connections),
                    hours_per_day=float(hours),
                    nat_gateways=int(nat_gateways),
                    nat_data_gb_per_month=nat_data_gb,
                    inuse_public_ipv4=int(inuse_ipv4),
                    idle_public_ipv4=int(idle_ipv4)
                )

                # Create VPC in service
                create_request = VPCCreateRequest(
                    service_ids=service_ids,
                    config=config,
                    name=f"VPC {vpc_id}"
                )

                # Manually set the VPC counter to match the loaded VPC ID
                vpc_service.vpc_counter = max(vpc_service.vpc_counter, vpc_id)

                # Create the VPC
                vpc_response = vpc_service.create_vpc(create_request)
                print(f"Restored VPC {vpc_id} with {len(service_ids)} services, cost: ${vpc_response.config.cost:.6f}")

            except Exception as e:
                print(f"Error restoring VPC {vpc_id_str}: {e}")
                continue

        print(f"Successfully restored {len(vpclist)} VPCs to VPC service")

    except Exception as e:
        print(f"Error in restore_vpc_data_to_service: {e}")
        import traceback
        traceback.print_exc()
