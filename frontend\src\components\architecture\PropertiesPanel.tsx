import React, { useState, useEffect } from 'react'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import ServiceIcon from '@/components/ui/ServiceIcon'
import { ArchitectureNode, ServiceConfiguration } from '@/types/architecture'
import { Trash2, Settings, DollarSign, Clock, Info } from 'lucide-react'
import { cn } from '@/lib/utils'

interface PropertiesPanelProps {
  selectedNode: ArchitectureNode | null
  onUpdateNode: (nodeId: string, updates: Partial<ArchitectureNode>) => void
  onDeleteNode: (nodeId: string) => void
  onCalculateCost: (nodeId: string) => void
  className?: string
}

export const PropertiesPanel: React.FC<PropertiesPanelProps> = ({
  selectedNode,
  onUpdateNode,
  onDeleteNode,
  onCalculateCost,
  className
}) => {
  const [localConfig, setLocalConfig] = useState<Record<string, any>>({})
  const [isCalculating, setIsCalculating] = useState(false)

  useEffect(() => {
    if (selectedNode) {
      setLocalConfig(selectedNode.data.config)
    }
  }, [selectedNode])

  const handleConfigChange = (key: string, value: any) => {
    const newConfig = { ...localConfig, [key]: value }
    setLocalConfig(newConfig)

    if (selectedNode) {
      onUpdateNode(selectedNode.id, {
        data: {
          ...selectedNode.data,
          config: newConfig
        }
      })
    }
  }

  const handleCalculateCost = async () => {
    if (!selectedNode) return

    setIsCalculating(true)
    try {
      await onCalculateCost(selectedNode.id)
    } finally {
      setIsCalculating(false)
    }
  }

  const getServiceConfiguration = (): ServiceConfiguration => {
    if (!selectedNode) return {}

    const service = selectedNode.data.service
    const config: ServiceConfiguration = {}

    // Define configuration based on service type
    switch (service.id) {
      case 'aws-lambda':
        // PyQt5 MapleGUI parameter names (from lambda_makespan_cost.py)
        config.workload = {
          type: 'number',
          label: 'Workload',
          value: localConfig.workload || localConfig.workload_invocations || 10,  // PyQt5 default: 10
          min: 1,
          required: true,
          description: 'Number of Lambda invocations (PyQt5 parameter name)'
        }
        config.memory_mb = {
          type: 'select',
          label: 'Memory (MB)',
          value: localConfig.memory_mb || 1024,  // PyQt5 default: 1024
          options: [128, 256, 512, 1024, 1769, 3583, 5307, 7076, 8845, 10240],
          required: true,
          description: 'Amount of memory allocated to the function'
        }
        config.function_defn = {
          type: 'text',
          label: 'Function Definition',
          value: localConfig.function_defn || localConfig.function_purpose || 'DTS_deepreader',  // PyQt5 parameter name
          required: true,
          description: 'Function purpose/definition (PyQt5 parameter name)'
        }
        config.memory_required = {
          type: 'number',
          label: 'Memory Required',
          value: localConfig.memory_required || 204,  // PyQt5 default: 204
          min: 1,
          required: true,
          description: 'Memory required for function execution'
        }
        config.function_purpose = {
          type: 'text',
          label: 'Function Purpose',
          value: localConfig.function_purpose || 'DTS_deepreader',
          required: true,
          description: 'Purpose or name of the Lambda function'
        }
        config.memory_required = {
          type: 'number',
          label: 'Memory Required (MB)',
          value: localConfig.memory_required || 204,
          min: 1,
          required: true,
          description: 'Memory required for function execution'
        }
        // Additional Lambda parameters
        config.timeout = {
          type: 'number',
          label: 'Timeout (seconds)',
          value: localConfig.timeout || 30,
          min: 1,
          max: 900,
          required: true,
          description: 'Maximum execution time'
        }
        config.runtime = {
          type: 'select',
          label: 'Runtime',
          value: localConfig.runtime || 'nodejs18.x',
          options: ['nodejs18.x', 'python3.9', 'python3.10', 'java11', 'dotnet6'],
          required: true,
          description: 'Lambda runtime environment'
        }
        break

      case 'aws-ec2':
        // GenAI/LLM Parameters
        config.instanceType = {
          type: 'select',
          label: 'Instance Type',
          value: localConfig.instanceType || 'Inferentia(Inf2.24xlarge)',
          options: ['Inferentia(Inf2.24xlarge)', 'CPU Instances', 'Gaudi(dl1.24xlarge)'],
          required: true,
          description: 'EC2 instance type for GenAI workloads'
        }
        config.LLMModel = {
          type: 'select',
          label: 'LLM Model',
          value: localConfig.LLMModel || 'llama_model_7b',
          options: ['llama_model_7b', 'llama_model_13b', 'llama_model_70b'],
          required: true,
          description: 'Large Language Model to deploy'
        }
        config.batchSize = {
          type: 'select',
          label: 'Batch Size',
          value: localConfig.batchSize || '1',
          options: ['1', '2', '4'],
          required: true,
          description: 'Batch size for model inference'
        }
        // Backend expected parameters (noticed in network payload)
        config.input_token = {
          type: 'number',
          label: 'Input Tokens',
          value: localConfig.input_token || localConfig.inputTokens || 50,
          min: 1,
          required: true,
          description: 'Number of input tokens for cost calculation'
        }
        config.output_token = {
          type: 'number',
          label: 'Output Tokens',
          value: localConfig.output_token || localConfig.outputTokens || 150,
          min: 1,
          required: true,
          description: 'Number of output tokens for cost calculation'
        }
        config.workload = {
          type: 'number',
          label: 'Workload',
          value: localConfig.workload || 1000,
          min: 1,
          required: true,
          description: 'Expected workload for cost calculation'
        }
        // Additional EC2 parameters from original MapleGUI
        config.memory = {
          type: 'number',
          label: 'Memory (MB)',
          value: localConfig.memory || 10240,
          min: 512,
          required: true,
          description: 'Memory allocation for the instance'
        }
        config.cores = {
          type: 'number',
          label: 'CPU Cores',
          value: localConfig.cores || 4,
          min: 1,
          required: true,
          description: 'Number of CPU cores'
        }
        break

      case 'aws-s3':
        // PyQt5 MapleGUI parameter names (from s3_makespan_cost.py)
        config.workload = {
          type: 'number',
          label: 'Workload',
          value: localConfig.workload || 10,  // PyQt5 default: 10
          min: 1,
          required: true,
          description: 'Number of S3 operations (PyQt5 parameter name)'
        }
        config.fileSize = {
          type: 'number',
          label: 'File Size (MB)',
          value: localConfig.fileSize || localConfig.file_size || 100,  // PyQt5 param name, default: 100
          min: 1,
          required: true,
          description: 'Average file size in MB (PyQt5 parameter name)'
        }
        config.memoryConfig = {
          type: 'number',
          label: 'Memory Configuration',
          value: localConfig.memoryConfig || localConfig.memory || 1024,  // PyQt5 param name, default: 1024
          min: 512,
          required: true,
          description: 'Memory configuration for S3 operations (PyQt5 parameter name)'
        }
        config.operation = {
          type: 'select',
          label: 'Operation Type',
          value: localConfig.operation || 'read',  // PyQt5 default: 'read'
          options: ['read', 'write', 'delete', 'list'],
          required: true,
          description: 'Type of S3 operation'
        }
        // Additional S3 parameters
        config.storageClass = {
          type: 'select',
          label: 'Storage Class',
          value: localConfig.storageClass || 'Standard',
          options: ['Standard', 'Standard-IA', 'Glacier', 'Glacier Deep Archive'],
          required: true,
          description: 'S3 storage class'
        }
        config.storageGB = {
          type: 'number',
          label: 'Storage (GB)',
          value: localConfig.storageGB || 1000,
          min: 1,
          required: true,
          description: 'Total storage in GB'
        }
        config.requestsPerMonth = {
          type: 'number',
          label: 'Requests per Month',
          value: localConfig.requestsPerMonth || 100000,
          min: 1,
          required: true,
          description: 'Number of requests per month'
        }
        break

      case 'aws-dynamodb':
        // Backend expected parameters (from backend/app/models/requests.py)
        config.workload = {
          type: 'number',
          label: 'Workload Operations',
          value: localConfig.workload || 1,  // Changed default from 1000 to 1 as requested
          min: 1,
          required: true,
          description: 'Base workload operations (default: 1)'
        }
        config.throughput = {
          type: 'number',
          label: 'Throughput (req/sec)',
          value: localConfig.throughput || 1000,  // PyQt5 UI default from Diagram.py
          min: 1,
          required: true,
          description: 'Throughput in requests per second (PyQt5 UI default: 1000)'
        }
        config.data_size = {
          type: 'number',
          label: 'Data Size (MB)',
          value: localConfig.data_size || 10,
          min: 1,
          required: true,
          description: 'Data size in MB'
        }
        config.mem_config = {
          type: 'number',
          label: 'Memory Configuration',
          value: localConfig.mem_config || 8,
          min: 1,
          required: true,
          description: 'Memory configuration for DynamoDB'
        }
        config.chunk_size = {
          type: 'number',
          label: 'Chunk Size',
          value: localConfig.chunk_size || 14,
          min: 1,
          required: true,
          description: 'Chunk size for data processing'
        }
        config.num_tables = {
          type: 'number',
          label: 'Number of Tables',
          value: localConfig.num_tables || 1,
          min: 1,
          required: true,
          description: 'Number of DynamoDB tables'
        }
        config.num_threads = {
          type: 'number',
          label: 'Number of Threads',
          value: localConfig.num_threads || 1,
          min: 1,
          required: true,
          description: 'Number of processing threads'
        }
        // Additional DynamoDB parameters
        config.billingMode = {
          type: 'select',
          label: 'Billing Mode',
          value: localConfig.billingMode || 'On-Demand',
          options: ['On-Demand', 'Provisioned'],
          required: true,
          description: 'DynamoDB billing mode'
        }
        break

      case 'aws-sagemaker':
        config.instanceType = {
          type: 'select',
          label: 'Instance Type',
          value: localConfig.instanceType || 'ml.m5.2xlarge',
          options: ['ml.t3.medium', 'ml.m5.large', 'ml.m5.xlarge', 'ml.m5.2xlarge', 'ml.p3.2xlarge'],
          required: true,
          description: 'SageMaker instance type'
        }
        config.component = {
          type: 'select',
          label: 'Component',
          value: localConfig.component || 'Hosting',
          options: ['Training', 'Hosting', 'Processing', 'Inference'],
          required: true,
          description: 'SageMaker component type'
        }
        config.workload = {
          type: 'number',
          label: 'Workload',
          value: localConfig.workload || 1000,
          min: 1,
          required: true,
          description: 'Expected workload'
        }
        break

      case 'aws-bedrock':
        config.model = {
          type: 'select',
          label: 'Foundation Model',
          value: localConfig.model || 'claude-v2',
          options: ['claude-v2', 'claude-instant', 'titan-text', 'jurassic-2'],
          required: true,
          description: 'Foundation model to use'
        }
        config.inputTokens = {
          type: 'number',
          label: 'Input Tokens',
          value: localConfig.inputTokens || 1000,
          min: 1,
          required: true,
          description: 'Number of input tokens'
        }
        config.outputTokens = {
          type: 'number',
          label: 'Output Tokens',
          value: localConfig.outputTokens || 500,
          min: 1,
          required: true,
          description: 'Number of output tokens'
        }
        config.workload = {
          type: 'number',
          label: 'Workload',
          value: localConfig.workload || 1000,
          min: 1,
          required: true,
          description: 'Expected workload'
        }
        break

      case 'aws-api-gateway':
        // PyQt5 MapleGUI exact parameter names from Excel paramList
        config['Requests(/hour)'] = {
          type: 'number',
          label: 'Requests(/hour)',
          value: localConfig['Requests(/hour)'] || localConfig.requestsPerHour || 1000,
          min: 1,
          required: true,
          description: 'Number of API requests per hour (PyQt5 parameter name)'
        }
        config['Input payload size(KB)'] = {
          type: 'number',
          label: 'Input payload size(KB)',
          value: localConfig['Input payload size(KB)'] || localConfig.inputPayloadSize || 1,
          min: 1,
          required: true,
          description: 'Average input payload size in KB (PyQt5 parameter name)'
        }
        config['Output payload size(KB)'] = {
          type: 'number',
          label: 'Output payload size(KB)',
          value: localConfig['Output payload size(KB)'] || localConfig.outputPayloadSize || 1,
          min: 1,
          required: true,
          description: 'Average output payload size in KB (PyQt5 parameter name)'
        }
        // Backward compatibility parameters
        config.type = {
          type: 'select',
          label: 'API Type',
          value: localConfig.type || 'HTTP',
          options: ['HTTP', 'REST', 'WebSocket'],
          required: true,
          description: 'API Gateway type'
        }
        config.workload = {
          type: 'number',
          label: 'Workload',
          value: localConfig.workload || 1000,
          min: 1,
          required: true,
          description: 'Expected workload'
        }
        break

      default:
        // Generic configuration for other services - show all available parameters
        Object.keys(localConfig).forEach(key => {
          // Skip array/object parameters that are just options
          if (key.endsWith('Options') || Array.isArray(localConfig[key])) {
            return
          }

          const value = localConfig[key]
          let fieldType: 'text' | 'number' | 'select' | 'boolean' = 'text'

          // Determine field type based on value
          if (typeof value === 'number') {
            fieldType = 'number'
          } else if (typeof value === 'boolean') {
            fieldType = 'boolean'
          } else if (typeof value === 'string') {
            // Check if there are corresponding options
            const optionsKey = key + 'Options'
            if (localConfig[optionsKey] && Array.isArray(localConfig[optionsKey])) {
              fieldType = 'select'
            }
          }

          config[key] = {
            type: fieldType,
            label: key.charAt(0).toUpperCase() + key.slice(1).replace(/([A-Z])/g, ' $1'),
            value: value,
            options: fieldType === 'select' ? localConfig[key + 'Options'] : undefined,
            required: false,
            description: `${key.charAt(0).toUpperCase() + key.slice(1)} configuration parameter`
          }
        })
    }

    return config
  }

  const renderConfigField = (key: string, field: ServiceConfiguration[string]) => {
    switch (field.type) {
      case 'select':
        return (
          <Select
            value={String(field.value)}
            onValueChange={(value) => handleConfigChange(key, value)}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {field.options?.map(option => (
                <SelectItem key={String(option)} value={String(option)}>
                  {String(option)}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )

      case 'number':
        return (
          <Input
            type="number"
            value={field.value}
            onChange={(e) => handleConfigChange(key, Number(e.target.value))}
            min={field.min}
            max={field.max}
            step={field.step}
          />
        )

      case 'boolean':
        return (
          <Select
            value={String(field.value)}
            onValueChange={(value) => handleConfigChange(key, value === 'true')}
          >
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="true">Yes</SelectItem>
              <SelectItem value="false">No</SelectItem>
            </SelectContent>
          </Select>
        )

      default:
        return (
          <Input
            value={field.value}
            onChange={(e) => handleConfigChange(key, e.target.value)}
          />
        )
    }
  }

  if (!selectedNode) {
    return (
      <Card className={cn('h-full', className)}>
        <CardHeader>
          <CardTitle className="flex items-center space-x-2">
            <Settings className="h-5 w-5" />
            <span>Properties</span>
          </CardTitle>
          <CardDescription>
            Select a component to view and edit its properties
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="text-center py-8 text-muted-foreground">
            <Info className="h-12 w-12 mx-auto mb-4 opacity-50" />
            <p>No component selected</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  const service = selectedNode.data.service
  const configuration = getServiceConfiguration()

  return (
    <Card className={cn('h-full flex flex-col', className)}>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center space-x-2">
          <ServiceIcon
            icon={service.icon}
            fallbackIcon={service.fallbackIcon}
            alt={service.name}
            size="md"
          />
          <span className="truncate">{service.name}</span>
        </CardTitle>
        <div className="flex items-center space-x-2">
          <Badge variant="secondary">{service.provider}</Badge>
          <Badge variant="outline">{service.category}</Badge>
        </div>
        <CardDescription className="text-sm">
          {service.description}
        </CardDescription>
      </CardHeader>

      <CardContent className="flex-1 space-y-4 overflow-y-auto">
        {/* Cost and Performance Metrics */}
        {(selectedNode.data.cost !== undefined || selectedNode.data.latency !== undefined) && (
          <div className="space-y-3">
            <div className="flex items-center justify-between">
              <h4 className="text-sm font-medium">Performance Metrics</h4>
              <Button
                size="sm"
                variant="outline"
                onClick={handleCalculateCost}
                disabled={isCalculating}
                className="text-xs"
              >
                {isCalculating ? 'Calculating...' : 'Recalculate'}
              </Button>
            </div>

            <div className="grid grid-cols-2 gap-3">
              {selectedNode.data.cost !== undefined && (
                <div className="p-3 bg-green-50 border border-green-200 rounded-lg">
                  <div className="flex items-center space-x-1 text-green-700">
                    <DollarSign className="h-4 w-4" />
                    <span className="text-xs font-medium">Cost</span>
                  </div>
                  <div className="text-lg font-bold text-green-800">
                    ${selectedNode.data.cost.toFixed(4)}
                  </div>
                </div>
              )}

              {selectedNode.data.latency !== undefined && (
                <div className="p-3 bg-blue-50 border border-blue-200 rounded-lg">
                  <div className="flex items-center space-x-1 text-blue-700">
                    <Clock className="h-4 w-4" />
                    <span className="text-xs font-medium">Latency</span>
                  </div>
                  <div className="text-lg font-bold text-blue-800">
                    {selectedNode.data.latency.toFixed(2)}ms
                  </div>
                </div>
              )}
            </div>
            <Separator />
          </div>
        )}

        {/* Configuration */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Configuration</h4>
          <div className="space-y-3">
            {Object.entries(configuration).map(([key, field]) => (
              <div key={key} className="space-y-1">
                <Label htmlFor={key} className="text-xs">
                  {field.label}
                  {field.required && <span className="text-red-500 ml-1">*</span>}
                </Label>
                {renderConfigField(key, field)}
                {field.description && (
                  <p className="text-xs text-muted-foreground">
                    {field.description}
                  </p>
                )}
              </div>
            ))}
          </div>
        </div>

        <Separator />

        {/* Actions */}
        <div className="space-y-2">
          <h4 className="text-sm font-medium">Actions</h4>
          <Button
            variant="destructive"
            size="sm"
            onClick={() => onDeleteNode(selectedNode.id)}
            className="w-full"
          >
            <Trash2 className="h-4 w-4 mr-2" />
            Remove Component
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}

export default PropertiesPanel
